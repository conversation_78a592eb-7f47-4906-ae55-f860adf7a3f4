import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
	// Enable PWA features only in production
	...(process.env.NODE_ENV === 'production' && {
		// PWA configuration for production
		experimental: {
			// Enable service worker in production
			swcMinify: true,
		},
	}),

	// General configuration
	reactStrictMode: true,

	// Headers for PWA support
	async headers() {
		return [
			{
				source: '/site.webmanifest',
				headers: [
					{
						key: 'Content-Type',
						value: 'application/manifest+json',
					},
				],
			},
		];
	},
};

export default nextConfig;
